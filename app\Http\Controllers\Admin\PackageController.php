<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Package;
use App\Helpers\ImageHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class PackageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $packages = Package::latest()->get();
        return view('admin.packages.index', compact('packages'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.packages.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
        ]);

        $data = $request->only(['name', 'description', 'price']);

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = ImageHelper::uploadAndResize($request->file('image'), 'packages');
        }

        Package::create($data);

        return redirect()->route('admin.packages.index')
            ->with('success', 'เพิ่มแพ็กเกจสำเร็จ');
    }

    /**
     * Display the specified resource.
     */
    public function show(Package $package)
    {
        return view('admin.packages.show', compact('package'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Package $package)
    {
        return view('admin.packages.edit', compact('package'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Package $package)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
        ]);

        $data = $request->only(['name', 'description', 'price']);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($package->image && Storage::disk('public')->exists('packages/' . $package->image)) {
                Storage::disk('public')->delete('packages/' . $package->image);
            }
            
            $data['image'] = ImageHelper::uploadAndResize($request->file('image'), 'packages');
        }

        $package->update($data);

        return redirect()->route('admin.packages.index')
            ->with('success', 'อัปเดตแพ็กเกจสำเร็จ');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Package $package)
    {
        // Delete image if exists
        if ($package->image && Storage::disk('public')->exists('packages/' . $package->image)) {
            Storage::disk('public')->delete('packages/' . $package->image);
        }

        $package->delete();

        return redirect()->route('admin.packages.index')
            ->with('success', 'ลบแพ็กเกจสำเร็จ');
    }

    /**
     * Bulk delete packages
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:packages,id'
        ]);

        $packages = Package::whereIn('id', $request->ids)->get();
        
        foreach ($packages as $package) {
            // Delete image if exists
            if ($package->image && Storage::disk('public')->exists('packages/' . $package->image)) {
                Storage::disk('public')->delete('packages/' . $package->image);
            }
            $package->delete();
        }

        return response()->json([
            'success' => true,
            'message' => 'ลบแพ็กเกจที่เลือกสำเร็จ (' . count($request->ids) . ' รายการ)'
        ]);
    }
}
