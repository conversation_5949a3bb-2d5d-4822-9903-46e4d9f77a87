@extends('layouts.admin')

@section('title', 'จัดการหมวดหมู่กิจกรรม - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-tags me-2 text-info"></i>จัดการหมวดหมู่กิจกรรม
                    </h1>
                    <p class="text-muted">จัดการหมวดหมู่สำหรับจัดกลุ่มกิจกรรม</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-tags"></i> จัดการหมวดหมู่กิจกรรม
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Action Bar -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <button type="button" class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                                        <i class="fas fa-plus"></i> เพิ่มหมวดหมู่ใหม่
                                    </button>
                                    <span class="text-muted">ทั้งหมด {{ $categories->count() }} หมวดหมู่</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="input-group" style="width: 300px;">
                                        <input type="text" class="form-control" id="searchInput" placeholder="ค้นหาหมวดหมู่...">
                                        <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Categories Grid -->
            <div class="row" id="categoriesGrid">
                @forelse($categories as $category)
                    <div class="col-lg-4 col-md-6 col-12 mb-4 category-item" data-category-id="{{ $category->id }}">
                        <div class="card shadow-sm border-0 h-100 category-card">
                            <div class="card-header d-flex justify-content-between align-items-center" style="background-color: {{ $category->color }}; color: white;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-tag me-2"></i>
                                    <h6 class="mb-0">{{ $category->name }}</h6>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item edit-category" href="#" data-category-id="{{ $category->id }}">
                                                <i class="fas fa-edit text-info"></i> แก้ไข
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item toggle-status" href="#" data-category-id="{{ $category->id }}" data-active="{{ $category->is_active }}">
                                                @if($category->is_active)
                                                    <i class="fas fa-eye-slash text-secondary"></i> ปิดใช้งาน
                                                @else
                                                    <i class="fas fa-eye text-success"></i> เปิดใช้งาน
                                                @endif
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item delete-category" href="#" data-category-id="{{ $category->id }}">
                                                <i class="fas fa-trash text-danger"></i> ลบ
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        @if($category->is_active)
                                            <span class="badge bg-success">ใช้งาน</span>
                                        @else
                                            <span class="badge bg-secondary">ปิดใช้งาน</span>
                                        @endif
                                    </div>
                                    <div class="text-end">
                                        <h4 class="mb-0 text-primary">{{ $category->activities_count }}</h4>
                                        <small class="text-muted">กิจกรรม</small>
                                    </div>
                                </div>
                                
                                @if($category->description)
                                    <p class="card-text text-muted">{{ Str::limit($category->description, 100) }}</p>
                                @else
                                    <p class="card-text text-muted fst-italic">ไม่มีคำอธิบาย</p>
                                @endif
                                
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i> {{ $category->created_at->format('d/m/Y') }}
                                    </small>
                                    <div>
                                        <button class="btn btn-sm btn-info edit-category" data-category-id="{{ $category->id }}">
                                            <i class="fas fa-edit"></i> แก้ไข
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12">
                        <div class="card shadow-sm border-0">
                            <div class="card-body text-center py-5">
                                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">ยังไม่มีหมวดหมู่</h5>
                                <p class="text-muted">เริ่มต้นด้วยการเพิ่มหมวดหมู่แรกของคุณ</p>
                                <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                                    <i class="fas fa-plus"></i> เพิ่มหมวดหมู่ใหม่
                                </button>
                            </div>
                        </div>
                    </div>
                @endforelse
            </div>
        </div>
    </section>
</div>

<!-- Create/Edit Category Modal -->
<div class="modal fade" id="createCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> <span id="modalTitle">เพิ่มหมวดหมู่ใหม่</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="categoryForm">
                @csrf
                <input type="hidden" id="categoryId" name="category_id">
                <input type="hidden" id="formMethod" name="_method" value="POST">
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">ชื่อหมวดหมู่ <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบาย</label>
                        <textarea class="form-control" id="description" name="description" rows="3" placeholder="คำอธิบายเกี่ยวกับหมวดหมู่นี้ (ไม่บังคับ)"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="color" class="form-label">สีหมวดหมู่ <span class="text-danger">*</span></label>
                        <div class="d-flex align-items-center">
                            <input type="color" class="form-control form-control-color me-2" id="color" name="color" value="#007bff" required style="width: 60px;">
                            <input type="text" class="form-control" id="colorText" placeholder="#007bff" readonly>
                        </div>
                        <small class="form-text text-muted">เลือกสีที่จะใช้แสดงหมวดหมู่นี้</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                เปิดใช้งานหมวดหมู่นี้
                            </label>
                        </div>
                    </div>
                    
                    <!-- Color Preview -->
                    <div class="mb-3">
                        <label class="form-label">ตัวอย่างการแสดงผล</label>
                        <div class="p-3 rounded" id="colorPreview" style="background-color: #007bff; color: white;">
                            <i class="fas fa-tag me-2"></i>
                            <span id="previewName">ชื่อหมวดหมู่</span>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-save"></i> <span id="submitBtnText">บันทึก</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('styles')
<style>
.category-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}
.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}
.form-control-color {
    height: 38px;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Search functionality
    $('#searchInput').on('keyup', function() {
        let searchTerm = $(this).val().toLowerCase();
        $('.category-item').each(function() {
            let categoryName = $(this).find('h6').text().toLowerCase();
            let categoryDesc = $(this).find('.card-text').text().toLowerCase();

            if (categoryName.includes(searchTerm) || categoryDesc.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Color picker change
    $('#color').on('change', function() {
        let color = $(this).val();
        $('#colorText').val(color);
        $('#colorPreview').css('background-color', color);
    });

    // Name change for preview
    $('#name').on('keyup', function() {
        let name = $(this).val() || 'ชื่อหมวดหมู่';
        $('#previewName').text(name);
    });

    // Edit category
    $('.edit-category').on('click', function(e) {
        e.preventDefault();
        let categoryId = $(this).data('category-id');
        loadCategoryData(categoryId);
    });

    function loadCategoryData(categoryId) {
        $.get(`{{ url('admin/activity-categories') }}/${categoryId}`, function(data) {
            $('#categoryId').val(data.id);
            $('#name').val(data.name);
            $('#description').val(data.description);
            $('#color').val(data.color);
            $('#colorText').val(data.color);
            $('#is_active').prop('checked', data.is_active);
            $('#formMethod').val('PUT');
            $('#modalTitle').text('แก้ไขหมวดหมู่');
            $('#submitBtnText').text('อัปเดต');

            // Update preview
            $('#colorPreview').css('background-color', data.color);
            $('#previewName').text(data.name);

            $('#createCategoryModal').modal('show');
        });
    }

    // Toggle status
    $('.toggle-status').on('click', function(e) {
        e.preventDefault();
        let categoryId = $(this).data('category-id');
        let isActive = $(this).data('active');

        $.ajax({
            url: `{{ url('admin/activity-categories') }}/${categoryId}/toggle-status`,
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content'),
                is_active: !isActive
            },
            success: function() {
                location.reload();
            }
        });
    });

    // Reset modal when closed
    $('#createCategoryModal').on('hidden.bs.modal', function() {
        $('#categoryForm')[0].reset();
        $('#categoryId').val('');
        $('#formMethod').val('POST');
        $('#modalTitle').text('เพิ่มหมวดหมู่ใหม่');
        $('#submitBtnText').text('บันทึก');
        $('#color').val('#007bff');
        $('#colorText').val('#007bff');
        $('#colorPreview').css('background-color', '#007bff');
        $('#previewName').text('ชื่อหมวดหมู่');
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();
    });

    // Submit form
    $('#categoryForm').on('submit', function(e) {
        e.preventDefault();

        // Clear previous errors
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        let formData = $(this).serialize();
        let categoryId = $('#categoryId').val();
        let method = $('#formMethod').val();
        let url = categoryId ? `{{ url('admin/activity-categories') }}/${categoryId}` : '{{ route('admin.activity-categories.store') }}';

        $.ajax({
            url: url,
            type: method === 'PUT' ? 'PUT' : 'POST',
            data: formData,
            success: function(response) {
                $('#createCategoryModal').modal('hide');
                location.reload();
            },
            error: function(xhr) {
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    let errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(key) {
                        $(`#${key}`).addClass('is-invalid');
                        $(`#${key}`).after(`<div class="invalid-feedback">${errors[key][0]}</div>`);
                    });
                }
            }
        });
    });

    // Delete category
    $('.delete-category').on('click', function(e) {
        e.preventDefault();
        let categoryId = $(this).data('category-id');

        if (confirm('คุณแน่ใจหรือไม่ที่จะลบหมวดหมู่นี้? กิจกรรมที่อยู่ในหมวดหมู่นี้จะไม่มีหมวดหมู่')) {
            $.ajax({
                url: `{{ url('admin/activity-categories') }}/${categoryId}`,
                type: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function() {
                    location.reload();
                }
            });
        }
    });

    // Initialize color text on page load
    $('#colorText').val($('#color').val());
});
</script>
@endpush

@endsection
