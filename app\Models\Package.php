<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Package extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'price',
        'image',
    ];

    protected $casts = [
        'price' => 'decimal:2',
    ];

    /**
     * Get the image URL attribute
     */
    public function getImageUrlAttribute()
    {
        if ($this->image) {
            return asset('storage/packages/' . $this->image);
        }
        return asset('images/no-image.svg');
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute()
    {
        return '฿' . number_format($this->price, 0);
    }
}
