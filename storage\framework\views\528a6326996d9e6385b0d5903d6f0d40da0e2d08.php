<?php $__env->startSection('title', 'จัดการบริการ - Admin Panel'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-tools me-2 text-primary"></i>จัดการบริการ
                    </h1>
                    <p class="text-muted">จัดการบริการทั้งหมดของเว็บไซต์</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="<?php echo e(route('admin.dashboard')); ?>">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-tools"></i> จัดการบริการ
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Action Bar -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#createServiceModal">
                                        <i class="fas fa-plus"></i> เพิ่มบริการใหม่
                                    </button>
                                    <button type="button" class="btn btn-danger me-2" id="bulkDeleteBtn" style="display: none;">
                                        <i class="fas fa-trash"></i> ลบที่เลือก
                                    </button>
                                    <span class="text-muted" id="selectedCount"></span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="input-group" style="width: 300px;">
                                        <input type="text" class="form-control" id="searchInput" placeholder="ค้นหาบริการ...">
                                        <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Services Grid -->
            <div class="row" id="servicesGrid">
                <?php $__empty_1 = true; $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="col-lg-4 col-md-6 col-12 mb-4 service-item" data-service-id="<?php echo e($service->id); ?>">
                        <div class="card shadow-sm border-0 h-100 service-card">
                            <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                                <div class="form-check">
                                    <input class="form-check-input service-checkbox" type="checkbox" value="<?php echo e($service->id); ?>">
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item edit-service" href="#" data-service-id="<?php echo e($service->id); ?>">
                                                <i class="fas fa-edit text-primary"></i> แก้ไข
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item view-service" href="<?php echo e(route('services.show', $service)); ?>" target="_blank">
                                                <i class="fas fa-eye text-info"></i> ดูหน้าบ้าน
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item delete-service" href="#" data-service-id="<?php echo e($service->id); ?>">
                                                <i class="fas fa-trash text-danger"></i> ลบ
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="position-relative">
                                <img src="<?php echo e($service->image_url); ?>" class="card-img-top" style="height: 200px; object-fit: cover;" alt="<?php echo e($service->title); ?>">
                                <div class="position-absolute top-0 end-0 m-2">
                                    <span class="badge bg-primary"><?php echo e($service->formatted_price); ?></span>
                                </div>
                            </div>
                            
                            <div class="card-body">
                                <h5 class="card-title"><?php echo e($service->title); ?></h5>
                                <p class="card-text text-muted"><?php echo e(Str::limit($service->description, 100)); ?></p>
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i> <?php echo e($service->created_at->format('d/m/Y')); ?>

                                    </small>
                                    <div>
                                        <button class="btn btn-sm btn-primary edit-service" data-service-id="<?php echo e($service->id); ?>">
                                            <i class="fas fa-edit"></i> แก้ไข
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-12">
                        <div class="card shadow-sm border-0">
                            <div class="card-body text-center py-5">
                                <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">ยังไม่มีบริการ</h5>
                                <p class="text-muted">เริ่มต้นด้วยการเพิ่มบริการแรกของคุณ</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createServiceModal">
                                    <i class="fas fa-plus"></i> เพิ่มบริการใหม่
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>
</div>

<!-- Create/Edit Service Modal -->
<div class="modal fade" id="createServiceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> <span id="modalTitle">เพิ่มบริการใหม่</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="serviceForm" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="serviceId" name="service_id">
                <input type="hidden" id="formMethod" name="_method" value="POST">
                
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">ชื่อบริการ <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">รายละเอียดบริการ <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="price" class="form-label">ราคา (บาท) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="price" name="price" min="0" step="0.01" required>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="image" class="form-label">รูปภาพบริการ</label>
                                <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                <small class="form-text text-muted">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                            </div>
                            
                            <div id="imagePreview" class="text-center" style="display: none;">
                                <img id="previewImg" src="" class="img-thumbnail" style="max-width: 100%; max-height: 200px;">
                                <div class="mt-2">
                                    <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                        <i class="fas fa-trash"></i> ลบรูปภาพ
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> <span id="submitBtnText">บันทึก</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.service-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}
.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}
.service-checkbox:checked ~ .card {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Search functionality
    $('#searchInput').on('keyup', function() {
        let searchTerm = $(this).val().toLowerCase();
        $('.service-item').each(function() {
            let serviceTitle = $(this).find('.card-title').text().toLowerCase();
            let serviceDesc = $(this).find('.card-text').text().toLowerCase();

            if (serviceTitle.includes(searchTerm) || serviceDesc.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Checkbox selection
    $('.service-checkbox').on('change', function() {
        updateBulkActions();
    });

    function updateBulkActions() {
        let checkedBoxes = $('.service-checkbox:checked');
        if (checkedBoxes.length > 0) {
            $('#bulkDeleteBtn').show();
            $('#selectedCount').text(`เลือกแล้ว ${checkedBoxes.length} รายการ`);
        } else {
            $('#bulkDeleteBtn').hide();
            $('#selectedCount').text('');
        }
    }

    // Edit service
    $('.edit-service').on('click', function(e) {
        e.preventDefault();
        let serviceId = $(this).data('service-id');
        loadServiceData(serviceId);
    });

    function loadServiceData(serviceId) {
        $.get(`<?php echo e(url('admin/services')); ?>/${serviceId}`, function(data) {
            $('#serviceId').val(data.id);
            $('#title').val(data.title);
            $('#description').val(data.description);
            $('#price').val(data.price);
            $('#formMethod').val('PUT');
            $('#modalTitle').text('แก้ไขบริการ');
            $('#submitBtnText').text('อัปเดต');

            if (data.image_url) {
                $('#previewImg').attr('src', data.image_url);
                $('#imagePreview').show();
            }

            $('#createServiceModal').modal('show');
        }).fail(function(xhr) {
            console.log('Error loading service data:', xhr);
            alert('เกิดข้อผิดพลาดในการโหลดข้อมูล');
        });
    }

    // Reset modal when closed
    $('#createServiceModal').on('hidden.bs.modal', function() {
        $('#serviceForm')[0].reset();
        $('#serviceId').val('');
        $('#formMethod').val('POST');
        $('#modalTitle').text('เพิ่มบริการใหม่');
        $('#submitBtnText').text('บันทึก');
        $('#imagePreview').hide();
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();
    });

    // Image preview
    $('#image').on('change', function() {
        let file = this.files[0];
        if (file) {
            let reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove image
    $('#removeImage').on('click', function() {
        $('#image').val('');
        $('#imagePreview').hide();
    });

    // Submit form
    $('#serviceForm').on('submit', function(e) {
        e.preventDefault();

        // Clear previous errors
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        let formData = new FormData(this);
        let serviceId = $('#serviceId').val();
        let method = $('#formMethod').val();
        let url = serviceId ? `<?php echo e(url('admin/services')); ?>/${serviceId}` : '<?php echo e(route('admin.services.store')); ?>';

        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }

        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#createServiceModal').modal('hide');
                location.reload();
            },
            error: function(xhr) {
                console.log('Submit error:', xhr);
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    let errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(key) {
                        $(`#${key}`).addClass('is-invalid');
                        $(`#${key}`).after(`<div class="invalid-feedback">${errors[key][0]}</div>`);
                    });
                }
            }
        });
    });

    // Delete service
    $('.delete-service').on('click', function(e) {
        e.preventDefault();
        let serviceId = $(this).data('service-id');

        if (confirm('คุณแน่ใจหรือไม่ที่จะลบบริการนี้?')) {
            $.ajax({
                url: `<?php echo e(url('admin/services')); ?>/${serviceId}`,
                type: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function() {
                    location.reload();
                },
                error: function(xhr) {
                    console.log('Delete error:', xhr);
                    alert('เกิดข้อผิดพลาดในการลบข้อมูล');
                }
            });
        }
    });

    // Bulk delete
    $('#bulkDeleteBtn').on('click', function() {
        let selectedIds = $('.service-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (confirm(`คุณแน่ใจหรือไม่ที่จะลบบริการ ${selectedIds.length} รายการ?`)) {
            $.ajax({
                url: '<?php echo e(route('admin.services.bulk-delete')); ?>',
                type: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    ids: selectedIds
                },
                success: function() {
                    location.reload();
                },
                error: function(xhr) {
                    console.log('Bulk delete error:', xhr);
                    alert('เกิดข้อผิดพลาดในการลบข้อมูล');
                }
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/services/index.blade.php ENDPATH**/ ?>