<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ActivityCategory;
use Illuminate\Http\Request;

class ActivityCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $categories = ActivityCategory::withCount('activities')->get();
        return view('admin.activity-categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.activity-categories.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255|unique:activity_categories,name',
            'description' => 'nullable|string',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
        ]);

        $data['is_active'] = $request->has('is_active');

        $category = ActivityCategory::create($data);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'เพิ่มหมวดหมู่สำเร็จ',
                'category' => $category
            ]);
        }

        return redirect()->route('admin.activity-categories.index')->with('success', 'เพิ่มหมวดหมู่สำเร็จ');
    }

    /**
     * Display the specified resource for AJAX.
     */
    public function show(ActivityCategory $activityCategory)
    {
        if (request()->ajax()) {
            return response()->json($activityCategory);
        }

        return redirect()->route('admin.activity-categories.index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ActivityCategory $activityCategory)
    {
        return view('admin.activity-categories.edit', compact('activityCategory'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ActivityCategory $activityCategory)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255|unique:activity_categories,name,' . $activityCategory->id,
            'description' => 'nullable|string',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
        ]);

        $data['is_active'] = $request->has('is_active');

        $activityCategory->update($data);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'อัปเดตหมวดหมู่สำเร็จ',
                'category' => $activityCategory
            ]);
        }

        return redirect()->route('admin.activity-categories.index')->with('success', 'อัปเดตหมวดหมู่สำเร็จ');
    }

    /**
     * Toggle category status
     */
    public function toggleStatus(Request $request, ActivityCategory $activityCategory)
    {
        try {
            $activityCategory->update([
                'is_active' => $request->is_active
            ]);

            return response()->json([
                'success' => true,
                'message' => $request->is_active ? 'เปิดใช้งานหมวดหมู่แล้ว' : 'ปิดใช้งานหมวดหมู่แล้ว'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะ'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ActivityCategory $activityCategory)
    {
        // Check if category has activities
        if ($activityCategory->activities()->count() > 0) {
            return redirect()->route('admin.activity-categories.index')
                ->with('error', 'ไม่สามารถลบหมวดหมู่ที่มีกิจกรรมอยู่ได้');
        }

        $activityCategory->delete();

        return redirect()->route('admin.activity-categories.index')->with('success', 'ลบหมวดหมู่สำเร็จ');
    }
}
