@extends('layouts.admin')

@section('title', 'จัดการเนื้อหาหน้าแรก - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-home me-2 text-success"></i>จัดการเนื้อหาหน้าแรก
                    </h1>
                    <p class="text-muted">แก้ไขเนื้อหาและรูปภาพในหน้าแรกของเว็บไซต์</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-home"></i> จัดการเนื้อหาหน้าแรก
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Action Bar -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <span class="text-muted">ทั้งหมด {{ $homepage_contents->count() }} ส่วน</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <a href="{{ route('home') }}" target="_blank" class="btn btn-success me-2">
                                        <i class="fas fa-external-link-alt"></i> ดูหน้าเว็บไซต์
                                    </a>
                                    <button type="button" class="btn btn-primary" id="saveAllBtn">
                                        <i class="fas fa-save"></i> บันทึกทั้งหมด
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Homepage Content Sections -->
            <div class="row">
                @foreach($homepage_contents as $content)
                    <div class="col-12 mb-4">
                        <div class="card shadow-sm border-0 content-section" data-section="{{ $content->section }}">
                            <div class="card-header bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-edit text-success"></i>
                                        {{ ucfirst($content->section) }} Section
                                    </h5>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-outline-primary preview-section" data-content-id="{{ $content->id }}">
                                            <i class="fas fa-eye"></i> ตัวอย่าง
                                        </button>
                                        <button type="button" class="btn btn-sm btn-success save-section" data-content-id="{{ $content->id }}">
                                            <i class="fas fa-save"></i> บันทึก
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="card-body">
                                <form class="content-form" data-content-id="{{ $content->id }}">
                                    @csrf
                                    @method('PUT')

                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="mb-3">
                                                <label class="form-label">หัวข้อ</label>
                                                <input type="text" class="form-control" name="title" value="{{ $content->title }}" required>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">เนื้อหา</label>
                                                <textarea class="form-control" name="content" rows="4" required>{{ $content->content }}</textarea>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">ข้อความปุ่ม</label>
                                                        <input type="text" class="form-control" name="button_text" value="{{ $content->button_text }}" placeholder="เช่น: เรียนรู้เพิ่มเติม">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">ลิงก์ปุ่ม</label>
                                                        <input type="url" class="form-control" name="button_link" value="{{ $content->button_link }}" placeholder="https://example.com">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">รูปภาพ</label>
                                                <input type="file" class="form-control image-input" name="image" accept="image/*" data-content-id="{{ $content->id }}">
                                                <small class="form-text text-muted">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                                            </div>

                                            <div class="text-center">
                                                <label class="form-label">รูปภาพปัจจุบัน</label>
                                                <div class="current-image" id="currentImage{{ $content->id }}">
                                                    <img src="{{ \App\Helpers\ImageHelper::getImageUrl($content->image) }}"
                                                         class="img-thumbnail"
                                                         style="max-width: 100%; max-height: 200px;"
                                                         alt="รูปภาพปัจจุบัน">
                                                </div>
                                            </div>

                                            <div class="image-preview text-center mt-3" id="imagePreview{{ $content->id }}" style="display: none;">
                                                <label class="form-label">รูปภาพใหม่</label>
                                                <img class="preview-img img-thumbnail" style="max-width: 100%; max-height: 200px;">
                                                <div class="mt-2">
                                                    <button type="button" class="btn btn-sm btn-danger remove-image" data-content-id="{{ $content->id }}">
                                                        <i class="fas fa-trash"></i> ลบรูปภาพใหม่
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
</div>

@push('styles')
<style>
.content-section {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}
.content-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}
.form-control:focus, .form-select:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40,167,69,.25);
}
.save-success {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40,167,69,.25) !important;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Image preview
    $('.image-input').on('change', function() {
        let file = this.files[0];
        let contentId = $(this).data('content-id');

        if (file) {
            let reader = new FileReader();
            reader.onload = function(e) {
                $(`#imagePreview${contentId} .preview-img`).attr('src', e.target.result);
                $(`#imagePreview${contentId}`).show();
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove image
    $('.remove-image').on('click', function() {
        let contentId = $(this).data('content-id');
        $(`.image-input[data-content-id="${contentId}"]`).val('');
        $(`#imagePreview${contentId}`).hide();
    });

    // Save individual section
    $('.save-section').on('click', function() {
        let contentId = $(this).data('content-id');
        let form = $(`.content-form[data-content-id="${contentId}"]`);
        saveSection(contentId, form);
    });

    // Save all sections
    $('#saveAllBtn').on('click', function() {
        let savePromises = [];

        $('.content-form').each(function() {
            let contentId = $(this).data('content-id');
            savePromises.push(saveSection(contentId, $(this)));
        });

        Promise.all(savePromises).then(function() {
            showNotification('บันทึกทั้งหมดสำเร็จ!', 'success');
        }).catch(function() {
            showNotification('เกิดข้อผิดพลาดในการบันทึกบางส่วน', 'error');
        });
    });

    function saveSection(contentId, form) {
        return new Promise(function(resolve, reject) {
            let formData = new FormData();

            // Add form data
            form.find('input, textarea').each(function() {
                if ($(this).attr('type') === 'file') {
                    if (this.files[0]) {
                        formData.append($(this).attr('name'), this.files[0]);
                    }
                } else {
                    formData.append($(this).attr('name'), $(this).val());
                }
            });

            formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
            formData.append('_method', 'PUT');

            $.ajax({
                url: `/admin/homepage/${contentId}`,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    // Show success feedback
                    form.addClass('save-success');
                    setTimeout(function() {
                        form.removeClass('save-success');
                    }, 2000);

                    // Update current image if new image was uploaded
                    if (response.image_url) {
                        $(`#currentImage${contentId} img`).attr('src', response.image_url);
                        $(`#imagePreview${contentId}`).hide();
                        $(`.image-input[data-content-id="${contentId}"]`).val('');
                    }

                    showNotification(`บันทึกส่วน ${response.section} สำเร็จ!`, 'success');
                    resolve();
                },
                error: function(xhr) {
                    showNotification('เกิดข้อผิดพลาดในการบันทึก', 'error');
                    reject();
                }
            });
        });
    }

    // Preview section
    $('.preview-section').on('click', function() {
        let contentId = $(this).data('content-id');
        let form = $(`.content-form[data-content-id="${contentId}"]`);

        let title = form.find('input[name="title"]').val();
        let content = form.find('textarea[name="content"]').val();
        let buttonText = form.find('input[name="button_text"]').val();
        let buttonLink = form.find('input[name="button_link"]').val();

        let previewHtml = `
            <div class="preview-content">
                <h3>${title}</h3>
                <p>${content}</p>
                ${buttonText ? `<a href="${buttonLink || '#'}" class="btn btn-primary">${buttonText}</a>` : ''}
            </div>
        `;

        // Show preview modal
        showPreviewModal(previewHtml);
    });

    function showPreviewModal(content) {
        let modal = `
            <div class="modal fade" id="previewModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">ตัวอย่างการแสดงผล</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${content}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal
        $('#previewModal').remove();

        // Add and show new modal
        $('body').append(modal);
        $('#previewModal').modal('show');

        // Remove modal after hiding
        $('#previewModal').on('hidden.bs.modal', function() {
            $(this).remove();
        });
    }

    function showNotification(message, type) {
        let alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        let icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

        let notification = `
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                <i class="fas ${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        $('body').append(notification);

        // Auto remove after 3 seconds
        setTimeout(function() {
            $('.alert').fadeOut(function() {
                $(this).remove();
            });
        }, 3000);
    }

    // Auto-resize textareas
    $('textarea').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>
@endpush

@endsection